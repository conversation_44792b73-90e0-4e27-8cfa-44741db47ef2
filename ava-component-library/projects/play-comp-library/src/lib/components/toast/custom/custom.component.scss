@use '../toast-base.scss';

.ava-toast-custom {
  background: var(--toast-background);
  color: var(--color-text-primary);
  border: 1px solid var(--toast-border);

  // Allow custom styling to override defaults
  &[style*="background"] {
    border-color: rgba(255, 255, 255, 0.2);
  }

  // Close button styling for ava-button (custom can have different text colors) - using higher specificity instead of !important
  .ava-toast-close.ava-button {
    background: transparent;
    border: none;
    box-shadow: none;
  }

  // For light backgrounds, use darker hover states - using higher specificity instead of !important
  &:not([style*="background"]) .ava-toast-close.ava-button {
    &:hover {
      background: var(--color-surface-subtle-hover);
    }

    &:active {
      background: var(--color-surface-subtle-active);
    }
  }
}
